---
output:
  html_document: default
  word_document: default
  pdf_document:
    keep_tex: true
    extra_dependencies: ["graphicx", "float", "tikz", "xcolor"]

# Metadatos ICFES
icfes:
  competencia:
    - argumentacion
  nivel_dificultad: 2
  contenido:
    categoria: estadistica
    tipo: generico
  contexto: familiar
  eje_axial: eje4
  componente: aleatorio
---

```{r setup, include=FALSE}
# Configuración para todos los formatos de salida
Sys.setlocale(category = "LC_NUMERIC", locale = "C")
options(OutDec = ".")

# Configurar el motor LaTeX globalmente
options(tikzLatex = "pdflatex")
options(tikzXelatex = FALSE)
options(tikzLatexPackages = c(
  "\\usepackage{tikz}",
  "\\usepackage{colortbl}",
  "\\usepackage{xcolor}",
  "\\usepackage{graphicx}",
  "\\usepackage{float}"
))

library(exams)
library(reticulate)
library(digest)
library(testthat)
library(knitr)
library(stringr)

typ <- match_exams_device()
options(scipen = 999)
knitr::opts_chunk$set(
  warning = FALSE,
  message = FALSE,
  fig.showtext = FALSE,
  fig.cap = "",
  fig.keep = 'all',
  dev = c("png", "pdf"),
  dpi = 150,
  fig.pos = "H"
)

# Configuración para chunks de Python
knitr::knit_engines$set(python = function(options) {
  knitr::engine_output(options, options$code, '')
})

# Asegurar que Python esté correctamente configurado
use_python(Sys.which("python"), required = TRUE)

# Semilla aleatoria para diversidad de versiones
set.seed(sample(1:100000, 1))
```

```{r data_generation, echo=FALSE, results="hide"}
# Función principal de generación de datos para competencia ARGUMENTACIÓN
generar_datos <- function() {
  # Contextos aleatorios ampliados para mayor diversidad
  contextos <- list(
    list(lugar = "escuela", tipo = "estudiantes", unidad = "calificaciones", medida = "puntos obtenidos"),
    list(lugar = "hospital", tipo = "pacientes", unidad = "tiempo de espera", medida = "minutos"),
    list(lugar = "supermercado", tipo = "productos", unidad = "precios", medida = "pesos"),
    list(lugar = "gimnasio", tipo = "miembros", unidad = "edades", medida = "años"),
    list(lugar = "restaurante", tipo = "platos", unidad = "tiempo de preparación", medida = "minutos"),
    list(lugar = "biblioteca", tipo = "libros", unidad = "páginas", medida = "número de páginas"),
    list(lugar = "empresa", tipo = "empleados", unidad = "años de experiencia", medida = "años"),
    list(lugar = "clínica", tipo = "consultas", unidad = "duración", medida = "minutos"),
    list(lugar = "tienda", tipo = "artículos", unidad = "precios", medida = "miles de pesos"),
    list(lugar = "universidad", tipo = "cursos", unidad = "estudiantes inscritos", medida = "número de estudiantes")
  )

  contexto_sel <- sample(contextos, 1)[[1]]

  # Aleatorizar número de datos entre 7 y 11 (para argumentación más compleja)
  n_datos <- sample(7:11, 1)

  # Generar conjunto de datos realista
  mediana_real <- sample(15:45, 1)
  
  # Generar datos con distribución controlada
  if(n_datos %% 2 == 1) {
    # Caso impar: mediana es el valor central
    pos_mediana <- (n_datos + 1) / 2
    n_menores <- pos_mediana - 1
    n_mayores <- n_datos - pos_mediana
    
    # Generar valores menores
    if(n_menores > 0) {
      valores_menores <- sample((mediana_real - 15):(mediana_real - 1), n_menores, replace = FALSE)
    } else {
      valores_menores <- c()
    }
    
    # Generar valores mayores
    if(n_mayores > 0) {
      valores_mayores <- sample((mediana_real + 1):(mediana_real + 15), n_mayores, replace = FALSE)
    } else {
      valores_mayores <- c()
    }
    
    datos_completos <- c(valores_menores, mediana_real, valores_mayores)
    
  } else {
    # Caso par: mediana es promedio de dos valores centrales
    pos1 <- n_datos / 2
    pos2 <- pos1 + 1
    
    # Generar dos valores centrales que promediados den la mediana
    val_central1 <- mediana_real + sample(c(-2, -1, 1, 2), 1)
    val_central2 <- 2 * mediana_real - val_central1
    
    # Generar valores menores
    n_menores <- pos1 - 1
    if(n_menores > 0) {
      valores_menores <- sample((mediana_real - 15):(min(val_central1, val_central2) - 1), n_menores, replace = FALSE)
    } else {
      valores_menores <- c()
    }
    
    # Generar valores mayores
    n_mayores <- n_datos - pos2
    if(n_mayores > 0) {
      valores_mayores <- sample((max(val_central1, val_central2) + 1):(mediana_real + 15), n_mayores, replace = FALSE)
    } else {
      valores_mayores <- c()
    }
    
    datos_completos <- c(valores_menores, val_central1, val_central2, valores_mayores)
  }
  
  # Ordenar datos
  datos_ordenados <- sort(datos_completos)
  
  # Calcular mediana real
  if(n_datos %% 2 == 1) {
    mediana_calculada <- datos_ordenados[(n_datos + 1) / 2]
  } else {
    pos1 <- n_datos / 2
    pos2 <- pos1 + 1
    mediana_calculada <- (datos_ordenados[pos1] + datos_ordenados[pos2]) / 2
  }
  
  # Generar afirmaciones para evaluar (COMPETENCIA ARGUMENTACIÓN)
  afirmaciones <- list()
  
  # Afirmación CORRECTA (siempre una)
  if(n_datos %% 2 == 1) {
    afirmacion_correcta <- paste0("La mediana es ", mediana_calculada, " porque es el valor que ocupa la posición central cuando los datos están ordenados")
  } else {
    afirmacion_correcta <- paste0("La mediana es ", mediana_calculada, " porque es el promedio de los dos valores centrales (", datos_ordenados[n_datos/2], " y ", datos_ordenados[n_datos/2 + 1], ")")
  }
  
  # SISTEMA AMPLIADO DE DISTRACTORES (8+ opciones para mayor diversidad)
  afirmaciones_incorrectas <- c()

  # Calcular valores necesarios para los distractores
  media_calculada <- round(mean(datos_ordenados), 1)
  tabla_freq <- table(datos_ordenados)

  # DECISIÓN ALEATORIA: ¿Permitir valores duplicados con justificaciones diferentes?
  # 30% de probabilidad de generar opciones con mismo valor pero diferentes justificaciones
  permitir_valores_duplicados <- sample(c(TRUE, FALSE), 1, prob = c(0.3, 0.7))

  # DISTRACTOR 1: Confundir mediana con media
  afirmaciones_incorrectas <- c(afirmaciones_incorrectas,
    paste0("La mediana es ", media_calculada, " porque se calcula sumando todos los valores y dividiendo por el número de datos"))

  # DISTRACTOR 2: Confundir mediana con moda o valor mínimo
  if(max(tabla_freq) > 1) {
    moda <- as.numeric(names(tabla_freq)[which.max(tabla_freq)])
    afirmaciones_incorrectas <- c(afirmaciones_incorrectas,
      paste0("La mediana es ", moda, " porque es el valor que más se repite en el conjunto de datos"))
  } else {
    afirmaciones_incorrectas <- c(afirmaciones_incorrectas,
      paste0("La mediana es ", datos_ordenados[1], " porque no hay valores repetidos, entonces se toma el menor"))
  }

  # DISTRACTOR 3: Posición incorrecta (primer o último valor)
  valor_extremo <- sample(c(datos_ordenados[1], datos_ordenados[n_datos]), 1)
  if(valor_extremo == datos_ordenados[1]) {
    afirmaciones_incorrectas <- c(afirmaciones_incorrectas,
      paste0("La mediana es ", valor_extremo, " porque es el primer valor cuando los datos están ordenados"))
  } else {
    afirmaciones_incorrectas <- c(afirmaciones_incorrectas,
      paste0("La mediana es ", valor_extremo, " porque es el último valor cuando los datos están ordenados"))
  }

  # DISTRACTOR 4: Error en cálculo para datos pares/impares
  if(n_datos %% 2 == 0) {
    # Para pares: sumar sin dividir
    suma_incorrecta <- datos_ordenados[n_datos/2] + datos_ordenados[n_datos/2 + 1]
    afirmaciones_incorrectas <- c(afirmaciones_incorrectas,
      paste0("La mediana es ", suma_incorrecta, " porque se suman los dos valores centrales sin dividir"))
  } else {
    # Para impares: promediar tres valores centrales
    pos_central <- (n_datos + 1) / 2
    if(pos_central > 1 && pos_central < n_datos) {
      promedio_tres <- round((datos_ordenados[pos_central - 1] + datos_ordenados[pos_central] + datos_ordenados[pos_central + 1]) / 3, 1)
      afirmaciones_incorrectas <- c(afirmaciones_incorrectas,
        paste0("La mediana es ", promedio_tres, " porque se promedian los tres valores centrales"))
    }
  }

  # DISTRACTOR 5: Valor en posición incorrecta específica
  if(n_datos >= 3) {
    pos_incorrecta <- sample(c(2, n_datos-1), 1)
    afirmaciones_incorrectas <- c(afirmaciones_incorrectas,
      paste0("La mediana es ", datos_ordenados[pos_incorrecta], " porque está en la posición ", pos_incorrecta, " de los datos ordenados"))
  }

  # DISTRACTOR 6: Confusión con rango o valor máximo
  rango <- max(datos_ordenados) - min(datos_ordenados)
  afirmaciones_incorrectas <- c(afirmaciones_incorrectas,
    paste0("La mediana es ", max(datos_ordenados), " porque es el valor máximo del conjunto de datos"))

  # DISTRACTOR 7: Error conceptual adicional (promedio de extremos)
  promedio_extremos <- round((datos_ordenados[1] + datos_ordenados[n_datos]) / 2, 1)
  afirmaciones_incorrectas <- c(afirmaciones_incorrectas,
    paste0("La mediana es ", promedio_extremos, " porque se promedian el valor mínimo y máximo"))

  # DISTRACTOR 8: Valor central incorrecto para pares
  if(n_datos %% 2 == 0) {
    valor_central_incorrecto <- datos_ordenados[sample(c(n_datos/2 - 1, n_datos/2 + 2), 1)]
    afirmaciones_incorrectas <- c(afirmaciones_incorrectas,
      paste0("La mediana es ", valor_central_incorrecto, " porque es uno de los valores cercanos al centro"))
  }
